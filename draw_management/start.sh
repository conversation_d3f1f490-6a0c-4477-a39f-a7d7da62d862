#!/bin/bash

# 图纸管理系统启动脚本

echo "=========================================="
echo "      图纸管理系统启动脚本"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 检查Python环境
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查Node.js环境
echo "检查Node.js环境..."
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm，请先安装Node.js"
    exit 1
fi

# 安装后端依赖
echo "安装后端依赖..."
cd backend
if [ ! -f ".env" ]; then
    echo "创建环境配置文件..."
    cp .env.example .env
fi

pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 后端依赖安装失败"
    exit 1
fi

# 初始化数据库
echo "初始化数据库..."
python3 init_db.py
if [ $? -ne 0 ]; then
    echo "错误: 数据库初始化失败"
    exit 1
fi

# 启动后端服务
echo "启动后端服务..."
PYTHONPATH=/Users/<USER>/Documents/code/self_project/draw_management/backend python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 安装前端依赖
echo "安装前端依赖..."
cd ../frontend
if [ ! -f ".env" ]; then
    echo "创建前端环境配置文件..."
    cp .env.example .env
fi

npm install
if [ $? -ne 0 ]; then
    echo "错误: 前端依赖安装失败"
    kill $BACKEND_PID
    exit 1
fi

# 启动前端服务
echo "启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "=========================================="
echo "      系统启动成功！"
echo "=========================================="
echo "前端地址: http://localhost:5173"
echo "后端API文档: http://localhost:8000/docs"
echo ""
echo "默认管理员账户:"
echo "用户名: admin"
echo "密码: admin123"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================================="

# 等待用户中断
trap 'echo "正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID; exit 0' INT
wait
