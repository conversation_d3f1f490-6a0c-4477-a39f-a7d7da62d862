# 图纸管理系统

一个现代化的图纸管理系统，采用 Python FastAPI + Vue3 + PostgreSQL 技术栈构建，具有简洁优雅的苹果风格UI设计。

## 功能特性

### 核心功能
- **产品数据管理** - 图纸查询、产品信息展示
- **委外图纸管理** - 外包图纸的管理和查询
- **设计模板管理** - 设计模板的存储和分类
- **培训资料管理** - 培训文档的管理
- **外网链接管理** - 外部资源链接管理
- **资源信息共享** - 文档共享和搜索
- **权限管理** - 用户角色和权限控制
- **个人工作流程** - 工作流程管理

### 技术特性
- 🚀 现代化技术栈
- 🎨 苹果风格UI设计
- 📱 响应式设计，支持移动端
- 🔐 JWT认证和基于角色的权限控制
- 📁 文件上传和管理
- 🔍 全文搜索功能
- 📊 数据统计和报表
- ⚡ 高性能API接口

## 技术栈

### 后端
- **Python 3.8+**
- **FastAPI** - 现代化的Web框架
- **SQLAlchemy** - ORM数据库操作
- **PostgreSQL** - 主数据库
- **Alembic** - 数据库迁移工具
- **JWT** - 身份认证
- **Uvicorn** - ASGI服务器

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Tailwind CSS** - 原子化CSS框架
- **Axios** - HTTP客户端

## 项目结构

```
draw_management/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模式
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── requirements.txt    # Python依赖
│   └── .env.example       # 环境配置示例
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # Vue组件
│   │   ├── stores/        # Pinia状态管理
│   │   ├── views/         # 页面组件
│   │   └── router/        # 路由配置
│   ├── package.json       # Node.js依赖
│   └── .env.example      # 环境配置示例
├── database/              # 数据库脚本
│   └── init.sql          # 初始化脚本
└── README.md             # 项目文档
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+

### 1. 克隆项目
```bash
git clone <repository-url>
cd draw_management
```

### 2. 后端设置

#### 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

#### 初始化数据库
```bash
# 创建数据库并运行初始化脚本
psql -U postgres -c "CREATE DATABASE draw_management;"
psql -U postgres -d draw_management -f ../database/init.sql
```

#### 启动后端服务
```bash
cd app
python main.py
# 或使用 uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 前端设置

#### 安装依赖
```bash
cd frontend
npm install
```

#### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置API地址等信息
```

#### 启动前端服务
```bash
npm run dev
```

### 4. 访问应用
- 前端地址: http://localhost:5173
- 后端API文档: http://localhost:8000/docs
- 后端管理界面: http://localhost:8000/redoc

## 默认账户

系统初始化后会创建以下默认账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 系统管理员账户 |
| designer1 | admin123 | 设计师 | 设计师账户 |
| engineer1 | admin123 | 工程师 | 工程师账户 |
| viewer1 | admin123 | 查看者 | 查看者账户 |

## 开发指南

### 后端开发
1. 在 `backend/app/models/` 中定义数据模型
2. 在 `backend/app/schemas/` 中定义API模式
3. 在 `backend/app/services/` 中实现业务逻辑
4. 在 `backend/app/api/` 中定义API路由

### 前端开发
1. 在 `frontend/src/components/` 中创建可复用组件
2. 在 `frontend/src/views/` 中创建页面组件
3. 在 `frontend/src/stores/` 中管理应用状态
4. 在 `frontend/src/api/` 中定义API接口

### 数据库迁移
```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head
```

## 部署

### Docker部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境配置
1. 修改环境变量配置
2. 配置反向代理（Nginx）
3. 设置SSL证书
4. 配置数据库备份

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-username/draw_management

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础图纸管理功能
- 用户认证和权限管理
- 苹果风格UI设计
