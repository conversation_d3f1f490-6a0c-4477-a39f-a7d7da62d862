<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>图纸管理系统 API 测试</h1>
    
    <div class="section">
        <h2>1. 用户登录</h2>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="login()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h2>2. 获取图纸列表</h2>
        <button onclick="getDrawings()">获取所有图纸</button>
        <button onclick="getDrawings('product')">获取产品图纸</button>
        <button onclick="getDrawings('outsourced')">获取委外图纸</button>
        <button onclick="getDrawings('template')">获取设计模板</button>
        <button onclick="getDrawings('training')">获取培训资料</button>
        <div id="drawingsResult" class="result"></div>
    </div>

    <div class="section">
        <h2>3. 创建图纸</h2>
        <input type="text" id="drawingNumber" placeholder="图纸编号" value="TEST-001">
        <input type="text" id="drawingTitle" placeholder="图纸标题" value="测试图纸">
        <textarea id="drawingDescription" placeholder="图纸描述">这是一个测试图纸</textarea>
        <select id="drawingType">
            <option value="product">产品图纸</option>
            <option value="outsourced">委外图纸</option>
            <option value="template">设计模板</option>
            <option value="training">培训资料</option>
        </select>
        <button onclick="createDrawing()">创建图纸</button>
        <div id="createResult" class="result"></div>
    </div>

    <script>
        let token = '';
        const API_BASE = 'http://localhost:8000';

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    token = data.access_token;
                    document.getElementById('loginResult').textContent = 
                        `登录成功！\nToken: ${token.substring(0, 50)}...`;
                } else {
                    document.getElementById('loginResult').textContent = 
                        `登录失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                document.getElementById('loginResult').textContent = 
                    `登录错误: ${error.message}`;
            }
        }

        async function getDrawings(type = '') {
            if (!token) {
                document.getElementById('drawingsResult').textContent = '请先登录！';
                return;
            }

            try {
                let url = `${API_BASE}/drawings/`;
                if (type) {
                    url += `?drawing_type=${type}`;
                }

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('drawingsResult').textContent = 
                        `获取成功！共 ${data.total} 条记录\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    document.getElementById('drawingsResult').textContent = 
                        `获取失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                document.getElementById('drawingsResult').textContent = 
                    `获取错误: ${error.message}`;
            }
        }

        async function createDrawing() {
            if (!token) {
                document.getElementById('createResult').textContent = '请先登录！';
                return;
            }

            const drawingData = {
                drawing_number: document.getElementById('drawingNumber').value,
                title: document.getElementById('drawingTitle').value,
                description: document.getElementById('drawingDescription').value,
                version: '1.0',
                drawing_type: document.getElementById('drawingType').value
            };

            try {
                const response = await fetch(`${API_BASE}/drawings/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(drawingData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('createResult').textContent = 
                        `创建成功！\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    document.getElementById('createResult').textContent = 
                        `创建失败: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                document.getElementById('createResult').textContent = 
                    `创建错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
