from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..core.database import Base


class UserRole(enum.Enum):
    """用户角色枚举"""

    ADMIN = "ADMIN"  # 管理员
    DESIGNER = "DESIGNER"  # 设计师
    ENGINEER = "ENGINEER"  # 工程师
    VIEWER = "VIEWER"  # 查看者
    GUEST = "GUEST"  # 访客


class User(Base):
    """用户模型"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    department = Column(String(100))
    position = Column(String(100))
    phone = Column(String(20))

    # 角色和权限
    role = Column(Enum(UserRole), default=UserRole.VIEWER, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))

    # 备注
    notes = Column(Text)

    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role.value}')>"
