from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "图纸管理系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./draw_management.db"

    # user = 'odoo'
    # password = urllib.parse.quote_plus('Drag0n@190916')
    # host = 'localhost'
    # port = 5483
    # db = 'draw_management'
    # DATABASE_URL = f"postgresql://{user}:{password}@{host}:{port}/{db}"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS: str = (
        "pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,jpeg,png,gif,dwg,dxf,step,stp"
    )

    # CORS配置
    BACKEND_CORS_ORIGINS: str = (
        "http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173"
    )

    @property
    def allowed_extensions_set(self) -> set:
        """获取允许的文件扩展名集合"""
        return set(self.ALLOWED_EXTENSIONS.split(","))

    @property
    def cors_origins_list(self) -> list:
        """获取CORS源列表"""
        return self.BACKEND_CORS_ORIGINS.split(",")

    class Config:
        env_file = ".env"


settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
