import os
import shutil
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, func
from typing import Optional, List
from fastapi import UploadFile
from ..models.drawing import Drawing
from ..models.user import User
from ..schemas.drawing import DrawingCreate, DrawingUpdate, DrawingSearch, DrawingList
from ..core.config import settings
import uuid


def get_drawing(db: Session, drawing_id: int) -> Optional[Drawing]:
    """根据ID获取图纸"""
    return (
        db.query(Drawing)
        .options(
            joinedload(Drawing.creator),
            joinedload(Drawing.updater),
            joinedload(Drawing.approver),
        )
        .filter(Drawing.id == drawing_id)
        .first()
    )


def get_drawing_by_number(db: Session, drawing_number: str) -> Optional[Drawing]:
    """根据图纸编号获取图纸"""
    return db.query(Drawing).filter(Drawing.drawing_number == drawing_number).first()


def get_drawings(db: Session, skip: int = 0, limit: int = 100) -> List[Drawing]:
    """获取图纸列表"""
    return (
        db.query(Drawing)
        .options(joinedload(Drawing.creator))
        .offset(skip)
        .limit(limit)
        .all()
    )


def create_drawing(db: Session, drawing: DrawingCreate, created_by: int) -> Drawing:
    """创建图纸"""
    # 检查图纸编号是否已存在
    if get_drawing_by_number(db, drawing_number=drawing.drawing_number):
        raise ValueError("图纸编号已存在")

    # 创建图纸记录
    db_drawing = Drawing(
        drawing_number=drawing.drawing_number,
        title=drawing.title,
        description=drawing.description,
        version=drawing.version,
        drawing_type=drawing.drawing_type,
        category=drawing.category,
        subcategory=drawing.subcategory,
        material=drawing.material,
        dimensions=drawing.dimensions,
        weight=drawing.weight,
        tolerance=drawing.tolerance,
        keywords=drawing.keywords,
        tags=drawing.tags,
        created_by=created_by,
    )

    db.add(db_drawing)
    db.commit()
    db.refresh(db_drawing)
    return db_drawing


def update_drawing(
    db: Session, drawing_id: int, drawing_update: DrawingUpdate, updated_by: int
) -> Optional[Drawing]:
    """更新图纸"""
    db_drawing = get_drawing(db, drawing_id=drawing_id)
    if not db_drawing:
        return None

    # 更新字段
    update_data = drawing_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_drawing, field, value)

    db_drawing.updated_by = updated_by

    db.commit()
    db.refresh(db_drawing)
    return db_drawing


def delete_drawing(db: Session, drawing_id: int) -> bool:
    """删除图纸"""
    db_drawing = get_drawing(db, drawing_id=drawing_id)
    if not db_drawing:
        return False

    # 删除关联文件
    if db_drawing.file_path and os.path.exists(db_drawing.file_path):
        os.remove(db_drawing.file_path)

    if db_drawing.thumbnail_path and os.path.exists(db_drawing.thumbnail_path):
        os.remove(db_drawing.thumbnail_path)

    db.delete(db_drawing)
    db.commit()
    return True


def search_drawings(db: Session, search_params: DrawingSearch) -> DrawingList:
    """搜索图纸"""
    query = db.query(Drawing).options(joinedload(Drawing.creator))

    # 关键字搜索
    if search_params.keyword:
        keyword = f"%{search_params.keyword}%"
        query = query.filter(
            or_(
                Drawing.title.ilike(keyword),
                Drawing.description.ilike(keyword),
                Drawing.drawing_number.ilike(keyword),
                Drawing.keywords.ilike(keyword),
                Drawing.tags.ilike(keyword),
            )
        )

    # 类型过滤
    if search_params.drawing_type:
        query = query.filter(Drawing.drawing_type == search_params.drawing_type)

    # 分类过滤
    if search_params.category:
        query = query.filter(Drawing.category == search_params.category)

    # 状态过滤
    if search_params.status:
        query = query.filter(Drawing.status == search_params.status)

    # 创建者过滤
    if search_params.created_by:
        query = query.filter(Drawing.created_by == search_params.created_by)

    # 日期范围过滤
    if search_params.date_from:
        query = query.filter(Drawing.created_at >= search_params.date_from)

    if search_params.date_to:
        query = query.filter(Drawing.created_at <= search_params.date_to)

    # 计算总数
    total = query.count()

    # 分页
    offset = (search_params.page - 1) * search_params.size
    items = query.offset(offset).limit(search_params.size).all()

    # 计算总页数
    pages = (total + search_params.size - 1) // search_params.size

    return DrawingList(
        items=items,
        total=total,
        page=search_params.page,
        size=search_params.size,
        pages=pages,
    )


async def upload_drawing_file(db: Session, drawing_id: int, file: UploadFile) -> dict:
    """上传图纸文件"""
    drawing = get_drawing(db, drawing_id=drawing_id)
    if not drawing:
        raise ValueError("图纸不存在")

    # 检查文件类型
    file_extension = file.filename.split(".")[-1].lower()
    if file_extension not in settings.allowed_extensions_set:
        raise ValueError(f"不支持的文件类型: {file_extension}")

    # 检查文件大小
    file_size = 0
    content = await file.read()
    file_size = len(content)

    if file_size > settings.MAX_FILE_SIZE:
        raise ValueError("文件大小超过限制")

    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}_{file.filename}"
    file_path = os.path.join(settings.UPLOAD_DIR, "drawings", unique_filename)

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存文件
    with open(file_path, "wb") as buffer:
        buffer.write(content)

    # 更新数据库记录
    drawing.file_path = file_path
    drawing.file_size = file_size
    drawing.file_format = file_extension

    db.commit()

    return {
        "filename": file.filename,
        "file_path": file_path,
        "file_size": file_size,
        "file_format": file_extension,
    }
