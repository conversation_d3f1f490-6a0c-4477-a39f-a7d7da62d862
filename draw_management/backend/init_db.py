#!/usr/bin/env python3
"""
数据库初始化脚本
用于开发环境快速初始化数据库和示例数据
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.core.database import Base, engine
from app.models.user import User, UserRole
from app.models.drawing import Drawing, DrawingType, DrawingStatus
from app.core.security import get_password_hash

def create_tables():
    """创建数据库表"""
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成!")

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查是否已有用户数据
        existing_user = db.query(User).first()
        if existing_user:
            print("数据库中已存在用户数据，跳过示例数据创建")
            return
        
        # 创建示例用户
        users_data = [
            {
                "username": "admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "系统管理员",
                "role": UserRole.ADMIN,
                "is_active": True,
                "is_verified": True
            },
            {
                "username": "designer1",
                "email": "<EMAIL>", 
                "password": "admin123",
                "full_name": "张设计师",
                "department": "设计部",
                "position": "高级设计师",
                "role": UserRole.DESIGNER
            },
            {
                "username": "engineer1",
                "email": "<EMAIL>",
                "password": "admin123", 
                "full_name": "李工程师",
                "department": "工程部",
                "position": "项目工程师",
                "role": UserRole.ENGINEER
            },
            {
                "username": "viewer1",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "王查看员", 
                "department": "质量部",
                "position": "质量检查员",
                "role": UserRole.VIEWER
            }
        ]
        
        created_users = []
        for user_data in users_data:
            password = user_data.pop("password")
            hashed_password = get_password_hash(password)
            
            user = User(
                **user_data,
                hashed_password=hashed_password
            )
            db.add(user)
            created_users.append(user)
        
        db.commit()
        
        # 刷新用户对象以获取ID
        for user in created_users:
            db.refresh(user)
        
        # 创建示例图纸
        drawings_data = [
            {
                "drawing_number": "DWG-001",
                "title": "主轴设计图",
                "description": "机械主轴的详细设计图纸",
                "drawing_type": DrawingType.PRODUCT,
                "category": "机械",
                "status": DrawingStatus.APPROVED,
                "created_by": created_users[1].id  # designer1
            },
            {
                "drawing_number": "DWG-002", 
                "title": "电路板布局图",
                "description": "PCB电路板的布局设计",
                "drawing_type": DrawingType.PRODUCT,
                "category": "电气",
                "status": DrawingStatus.REVIEW,
                "created_by": created_users[1].id  # designer1
            },
            {
                "drawing_number": "DWG-003",
                "title": "外壳模具图", 
                "description": "产品外壳的注塑模具图",
                "drawing_type": DrawingType.OUTSOURCED,
                "category": "模具",
                "status": DrawingStatus.DRAFT,
                "created_by": created_users[2].id  # engineer1
            },
            {
                "drawing_number": "TPL-001",
                "title": "标准图框模板",
                "description": "公司标准图纸框架模板", 
                "drawing_type": DrawingType.TEMPLATE,
                "category": "标准",
                "status": DrawingStatus.APPROVED,
                "created_by": created_users[0].id  # admin
            },
            {
                "drawing_number": "TRN-001",
                "title": "CAD操作手册",
                "description": "CAD软件使用培训手册",
                "drawing_type": DrawingType.TRAINING,
                "category": "培训", 
                "status": DrawingStatus.APPROVED,
                "created_by": created_users[0].id  # admin
            }
        ]
        
        for drawing_data in drawings_data:
            drawing = Drawing(**drawing_data)
            db.add(drawing)
        
        db.commit()
        print("示例数据创建完成!")
        
        # 打印创建的用户信息
        print("\n创建的用户账户:")
        print("=" * 50)
        for user_data in users_data:
            print(f"用户名: {user_data['username']}")
            print(f"密码: admin123")
            print(f"角色: {user_data['role'].value}")
            print(f"姓名: {user_data['full_name']}")
            print("-" * 30)
            
    except Exception as e:
        print(f"创建示例数据时出错: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """主函数"""
    print("=" * 50)
    print("图纸管理系统数据库初始化")
    print("=" * 50)
    
    try:
        # 测试数据库连接
        print("测试数据库连接...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("数据库连接成功!")
        
        # 创建表
        create_tables()
        
        # 创建示例数据
        create_sample_data()
        
        print("\n" + "=" * 50)
        print("数据库初始化完成!")
        print("=" * 50)
        print(f"数据库URL: {settings.DATABASE_URL}")
        print("您现在可以启动应用程序了")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
