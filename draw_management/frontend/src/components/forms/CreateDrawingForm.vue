<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- 基本信息 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label for="drawing_number" class="block text-sm font-medium text-gray-700 mb-1">
            图纸编号 <span class="text-red-500">*</span>
          </label>
          <input
            id="drawing_number"
            v-model="form.drawing_number"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入图纸编号"
          />
          <p v-if="errors.drawing_number" class="mt-1 text-sm text-red-600">
            {{ errors.drawing_number }}
          </p>
        </div>

        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
            图纸标题 <span class="text-red-500">*</span>
          </label>
          <input
            id="title"
            v-model="form.title"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入图纸标题"
          />
          <p v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</p>
        </div>

        <div>
          <label for="version" class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
          <input
            id="version"
            v-model="form.version"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="1.0"
          />
        </div>

        <div>
          <label for="drawing_type" class="block text-sm font-medium text-gray-700 mb-1">
            图纸类型 <span class="text-red-500">*</span>
          </label>
          <select
            id="drawing_type"
            v-model="form.drawing_type"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">请选择图纸类型</option>
            <option value="product">产品图纸</option>
            <option value="outsourced">委外图纸</option>
            <option value="template">设计模板</option>
            <option value="training">培训资料</option>
          </select>
          <p v-if="errors.drawing_type" class="mt-1 text-sm text-red-600">
            {{ errors.drawing_type }}
          </p>
        </div>
      </div>

      <div class="mt-4">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
        <textarea
          id="description"
          v-model="form.description"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="请输入图纸描述..."
        ></textarea>
      </div>
    </div>

    <!-- 分类信息 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">分类信息</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1"
            >产品类别</label
          >
          <input
            id="category"
            v-model="form.category"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入产品类别"
          />
        </div>

        <div>
          <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1"
            >子类别</label
          >
          <input
            id="subcategory"
            v-model="form.subcategory"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入子类别"
          />
        </div>
      </div>
    </div>

    <!-- 技术参数 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">技术参数</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label for="material" class="block text-sm font-medium text-gray-700 mb-1">材料</label>
          <input
            id="material"
            v-model="form.material"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入材料"
          />
        </div>

        <div>
          <label for="dimensions" class="block text-sm font-medium text-gray-700 mb-1">尺寸</label>
          <input
            id="dimensions"
            v-model="form.dimensions"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入尺寸"
          />
        </div>

        <div>
          <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">重量 (kg)</label>
          <input
            id="weight"
            v-model.number="form.weight"
            type="number"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入重量"
          />
        </div>

        <div>
          <label for="tolerance" class="block text-sm font-medium text-gray-700 mb-1">公差</label>
          <input
            id="tolerance"
            v-model="form.tolerance"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入公差"
          />
        </div>
      </div>
    </div>

    <!-- 关键字和标签 -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">关键字和标签</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label for="keywords" class="block text-sm font-medium text-gray-700 mb-1">关键字</label>
          <input
            id="keywords"
            v-model="form.keywords"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="用逗号分隔多个关键字"
          />
          <p class="mt-1 text-xs text-gray-500">用于搜索，多个关键字用逗号分隔</p>
        </div>

        <div>
          <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">标签</label>
          <input
            id="tags"
            v-model="form.tags"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="用逗号分隔多个标签"
          />
          <p class="mt-1 text-xs text-gray-500">用于分类，多个标签用逗号分隔</p>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        @click="$emit('cancel')"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        取消
      </button>
      <button
        type="submit"
        :disabled="loading"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="loading" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          创建中...
        </span>
        <span v-else>创建图纸</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

interface CreateDrawingForm {
  drawing_number: string;
  title: string;
  description: string;
  version: string;
  drawing_type: "product" | "outsourced" | "template" | "training" | "";
  category: string;
  subcategory: string;
  material: string;
  dimensions: string;
  weight: number | null;
  tolerance: string;
  keywords: string;
  tags: string;
}

interface FormErrors {
  [key: string]: string;
}

const emit = defineEmits<{
  submit: [form: CreateDrawingForm];
  cancel: [];
}>();

const loading = ref(false);
const errors = ref<FormErrors>({});

const form = reactive<CreateDrawingForm>({
  drawing_number: "",
  title: "",
  description: "",
  version: "1.0",
  drawing_type: "",
  category: "",
  subcategory: "",
  material: "",
  dimensions: "",
  weight: null,
  tolerance: "",
  keywords: "",
  tags: "",
});

const validateForm = (): boolean => {
  errors.value = {};

  if (!form.drawing_number.trim()) {
    errors.value.drawing_number = "图纸编号不能为空";
  }

  if (!form.title.trim()) {
    errors.value.title = "图纸标题不能为空";
  }

  if (!form.drawing_type) {
    errors.value.drawing_type = "请选择图纸类型";
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  loading.value = true;
  try {
    emit("submit", { ...form });
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    drawing_number: "",
    title: "",
    description: "",
    version: "1.0",
    drawing_type: "",
    category: "",
    subcategory: "",
    material: "",
    dimensions: "",
    weight: null,
    tolerance: "",
    keywords: "",
    tags: "",
  });
  errors.value = {};
};

defineExpose({
  resetForm,
});
</script>
