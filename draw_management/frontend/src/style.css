@tailwind base;
@tailwind components;
@tailwind utilities;

/* 强制重置所有SVG图标尺寸 */
svg {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  flex-shrink: 0 !important;
}

/* 确保SVG在容器内正确显示 */
svg[class*="h-"],
svg[class*="w-"] {
  flex-shrink: 0 !important;
}

/* 自定义动画延迟类 */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 玻璃态效果 */
.backdrop-blur-xl {
  backdrop-filter: blur(20px);
}

/* 渐变文字效果 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* 输入框聚焦效果 */
input:focus {
  transform: translateY(-1px);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}
