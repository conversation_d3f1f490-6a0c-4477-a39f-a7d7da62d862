<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl">
          {{ pageTitle }}
        </h1>
        <p class="mt-1 text-sm text-gray-500">管理和查看{{ typeText }}</p>
      </div>
      <div class="mt-4 flex space-x-3 md:ml-4 md:mt-0">
        <Button variant="secondary">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          导出
        </Button>
        <Button @click="showCreateModal = true">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          新建图纸
        </Button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <Card>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Input
          v-model="searchForm.keyword"
          placeholder="搜索图纸编号、标题..."
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <svg
              class="h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </template>
        </Input>

        <select
          v-model="searchForm.status"
          class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">所有状态</option>
          <option value="draft">草稿</option>
          <option value="review">审核中</option>
          <option value="approved">已批准</option>
          <option value="archived">已归档</option>
        </select>

        <select
          v-model="searchForm.category"
          class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">所有分类</option>
          <option value="mechanical">机械</option>
          <option value="electrical">电气</option>
          <option value="software">软件</option>
        </select>

        <div class="flex space-x-2">
          <Button @click="handleSearch" class="flex-1">搜索</Button>
          <Button variant="secondary" @click="handleReset">重置</Button>
        </div>
      </div>
    </Card>

    <!-- 图纸列表 -->
    <Card>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                图纸信息
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                类型/分类
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                状态
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                创建者
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                创建时间
              </th>
              <th
                class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="drawing in drawings" :key="drawing.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg
                      class="h-5 w-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ drawing.title }}</div>
                    <div class="text-sm text-gray-500">{{ drawing.drawing_number }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getTypeText(drawing.drawing_type) }}</div>
                <div class="text-sm text-gray-500">{{ drawing.category || "-" }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="getStatusColor(drawing.status)"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ getStatusText(drawing.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ drawing.creator_name || "-" }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(drawing.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-blue-600 hover:text-blue-900">查看</button>
                  <button class="text-gray-600 hover:text-gray-900">编辑</button>
                  <button class="text-red-600 hover:text-red-900">删除</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 空状态 -->
        <div v-if="drawings.length === 0" class="text-center py-12">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无图纸</h3>
          <p class="mt-1 text-sm text-gray-500">开始创建您的第一个图纸吧</p>
          <div class="mt-6">
            <Button @click="showCreateModal = true">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              新建图纸
            </Button>
          </div>
        </div>
      </div>
    </Card>

    <!-- 分页 -->
    <div v-if="pagination.pages > 1" class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示第 {{ (pagination.page - 1) * pagination.size + 1 }} 到
        {{ Math.min(pagination.page * pagination.size, pagination.total) }} 条， 共
        {{ pagination.total }} 条记录
      </div>
      <div class="flex space-x-2">
        <Button
          variant="secondary"
          :disabled="pagination.page <= 1"
          @click="changePage(pagination.page - 1)"
        >
          上一页
        </Button>
        <Button
          variant="secondary"
          :disabled="pagination.page >= pagination.pages"
          @click="changePage(pagination.page + 1)"
        >
          下一页
        </Button>
      </div>
    </div>

    <!-- 创建图纸模态框 -->
    <Modal :show="showCreateModal" @close="handleCloseCreateModal" title="创建新图纸" size="xl">
      <CreateDrawingForm
        ref="createFormRef"
        @submit="handleCreateDrawing"
        @cancel="handleCloseCreateModal"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useDrawingsStore } from "@/stores/drawings";
import Card from "@/components/ui/Card.vue";
import Input from "@/components/ui/Input.vue";
import Button from "@/components/ui/Button.vue";
import Modal from "@/components/ui/Modal.vue";
import CreateDrawingForm from "@/components/forms/CreateDrawingForm.vue";
import type { CreateDrawingRequest } from "@/api/drawings";
import { useToast } from "@/composables/useToast";

interface Props {
  drawingType?: "product" | "outsourced" | "template" | "training";
}

const props = withDefaults(defineProps<Props>(), {
  drawingType: undefined,
});

const drawingsStore = useDrawingsStore();
const showCreateModal = ref(false);
const createFormRef = ref<InstanceType<typeof CreateDrawingForm> | null>(null);
const toast = useToast();

// 搜索表单
const searchForm = ref({
  keyword: "",
  status: "",
  category: "",
});

// 计算属性
const drawings = computed(() => drawingsStore.drawings);
const pagination = computed(() => drawingsStore.pagination);
const loading = computed(() => drawingsStore.loading);

const pageTitle = computed(() => {
  const titles = {
    product: "产品图纸",
    outsourced: "委外图纸",
    template: "设计模板",
    training: "培训资料",
  };
  return props.drawingType ? titles[props.drawingType] : "所有图纸";
});

const typeText = computed(() => {
  const texts = {
    product: "产品相关的技术图纸",
    outsourced: "外包项目的图纸文档",
    template: "设计模板和标准",
    training: "培训和学习资料",
  };
  return props.drawingType ? texts[props.drawingType] : "所有类型的图纸文档";
});

// 方法
const handleSearch = async () => {
  const params = {
    ...searchForm.value,
    drawing_type: props.drawingType,
    page: 1,
  };
  await drawingsStore.fetchDrawings(params);
};

const handleReset = async () => {
  searchForm.value = {
    keyword: "",
    status: "",
    category: "",
  };
  await handleSearch();
};

const changePage = async (page: number) => {
  await drawingsStore.changePage(page);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("zh-CN");
};

const getStatusColor = (status: string) => {
  const colors = {
    draft: "bg-gray-100 text-gray-800",
    review: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    archived: "bg-blue-100 text-blue-800",
    obsolete: "bg-red-100 text-red-800",
  };
  return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

const getStatusText = (status: string) => {
  const texts = {
    draft: "草稿",
    review: "审核中",
    approved: "已批准",
    archived: "已归档",
    obsolete: "已废弃",
  };
  return texts[status as keyof typeof texts] || "未知";
};

const getTypeText = (type: string) => {
  const texts = {
    product: "产品图纸",
    outsourced: "委外图纸",
    template: "设计模板",
    training: "培训资料",
  };
  return texts[type as keyof typeof texts] || "未知";
};

// 创建图纸相关方法
const handleCreateDrawing = async (formData: any) => {
  try {
    // 转换表单数据为API请求格式
    const requestData: CreateDrawingRequest = {
      drawing_number: formData.drawing_number,
      title: formData.title,
      description: formData.description || undefined,
      version: formData.version,
      drawing_type: formData.drawing_type as "product" | "outsourced" | "template" | "training",
      category: formData.category || undefined,
      subcategory: formData.subcategory || undefined,
      material: formData.material || undefined,
      dimensions: formData.dimensions || undefined,
      weight: formData.weight || undefined,
      tolerance: formData.tolerance || undefined,
      keywords: formData.keywords || undefined,
      tags: formData.tags || undefined,
    };

    await drawingsStore.addDrawing(requestData);
    showCreateModal.value = false;
    createFormRef.value?.resetForm();
    toast.success("创建成功", "图纸已成功创建");
    // 刷新列表
    await handleSearch();
  } catch (error: any) {
    console.error("创建图纸失败:", error);
    toast.error("创建失败", error.message || "创建图纸时发生错误");
  }
};

const handleCloseCreateModal = () => {
  showCreateModal.value = false;
  createFormRef.value?.resetForm();
};

// 监听路由参数变化
watch(
  () => props.drawingType,
  () => {
    handleSearch();
  },
  { immediate: true }
);

// 初始化
onMounted(() => {
  handleSearch();
});
</script>
