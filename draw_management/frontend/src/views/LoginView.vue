<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo和标题 -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">图纸管理系统</h2>
        <p class="mt-2 text-sm text-gray-600">请登录您的账户</p>
      </div>

      <!-- 登录表单 -->
      <Card class="mt-8" :padding="false">
        <form @submit.prevent="handleLogin" class="p-8 space-y-6">
          <div class="space-y-4">
            <Input
              v-model="form.username"
              label="用户名"
              placeholder="请输入用户名"
              required
              :error="errors.username"
            />
            
            <Input
              v-model="form.password"
              type="password"
              label="密码"
              placeholder="请输入密码"
              required
              :error="errors.password"
            />
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="form.rememberMe"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                记住我
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                忘记密码？
              </a>
            </div>
          </div>

          <Button
            type="submit"
            :loading="loading"
            :disabled="!isFormValid"
            full-width
            class="w-full"
          >
            登录
          </Button>
        </form>
      </Card>

      <!-- 底部信息 -->
      <div class="text-center text-sm text-gray-500">
        <p>&copy; 2024 图纸管理系统. 保留所有权利.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Card from '@/components/ui/Card.vue'
import Input from '@/components/ui/Input.vue'
import Button from '@/components/ui/Button.vue'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = ref({
  username: '',
  password: '',
  rememberMe: false
})

// 错误信息
const errors = ref({
  username: '',
  password: ''
})

// 加载状态
const loading = ref(false)

// 表单验证
const isFormValid = computed(() => {
  return form.value.username.trim() !== '' && form.value.password.trim() !== ''
})

// 处理登录
const handleLogin = async () => {
  // 清除之前的错误
  errors.value = { username: '', password: '' }
  
  if (!isFormValid.value) {
    return
  }

  loading.value = true
  
  try {
    await authStore.login({
      username: form.value.username,
      password: form.value.password
    })
    
    // 登录成功，跳转到首页
    router.push('/')
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 处理错误信息
    if (error.response?.status === 401) {
      errors.value.password = '用户名或密码错误'
    } else {
      errors.value.password = '登录失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}
</script>
