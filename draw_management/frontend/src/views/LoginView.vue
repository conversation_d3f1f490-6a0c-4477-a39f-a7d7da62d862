<template>
  <div
    style="
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      background: linear-gradient(135deg, #f8fafc 0%, #dbeafe 50%, #e0e7ff 100%);
      position: relative;
    "
  >
    <!-- 背景装饰 -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; overflow: hidden; pointer-events: none;">
      <div
        style="
          position: absolute;
          top: -160px;
          right: -160px;
          width: 320px;
          height: 320px;
          background: #60a5fa;
          border-radius: 50%;
          mix-blend-mode: multiply;
          filter: blur(40px);
          opacity: 0.2;
          animation: blob 7s infinite;
        "
      ></div>
      <div
        style="
          position: absolute;
          bottom: -160px;
          left: -160px;
          width: 320px;
          height: 320px;
          background: #a78bfa;
          border-radius: 50%;
          mix-blend-mode: multiply;
          filter: blur(40px);
          opacity: 0.2;
          animation: blob 7s infinite;
          animation-delay: 2s;
        "
      ></div>
      <div
        style="
          position: absolute;
          top: 160px;
          left: 160px;
          width: 320px;
          height: 320px;
          background: #f472b6;
          border-radius: 50%;
          mix-blend-mode: multiply;
          filter: blur(40px);
          opacity: 0.2;
          animation: blob 7s infinite;
          animation-delay: 4s;
        "
      ></div>
    </div>

    <!-- 登录卡片 -->
    <div
      style="
        position: relative;
        z-index: 10;
        width: 100%;
        max-width: 448px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
      "
    >
      <!-- 头部区域 -->
      <div style="padding: 48px 32px 32px 32px; text-align: center">
        <!-- Logo -->
        <div
          style="
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3b82f6, #9333ea);
            border-radius: 16px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin: 0 auto 24px auto;
            display: flex;
            align-items: center;
            justify-content: center;
          "
          onmouseover="this.style.transform='scale(1.05)'"
          onmouseout="this.style.transform='scale(1)'"
        >
          <svg
            style="width: 28px; height: 28px; color: white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>

        <!-- 标题 -->
        <h1
          style="
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(to right, #111827, #4b5563);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 8px;
          "
        >
          图纸管理系统
        </h1>
        <p style="color: #6b7280; font-size: 0.875rem">欢迎回来，请登录您的账户</p>
      </div>

      <!-- 表单区域 -->
      <div style="padding: 0 32px 32px 32px">
        <form
          @submit.prevent="handleLogin"
          style="display: flex; flex-direction: column; gap: 24px"
        >
          <!-- 用户名输入 -->
          <div style="display: flex; flex-direction: column; gap: 8px">
            <label style="font-size: 0.875rem; font-weight: 500; color: #374151; display: block"
              >用户名</label
            >
            <div style="position: relative">
              <input
                v-model="form.username"
                type="text"
                required
                style="
                  width: 100%;
                  padding: 12px 16px;
                  background: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 12px;
                  outline: none;
                  transition: all 0.2s;
                  font-size: 1rem;
                "
                placeholder="请输入用户名"
                onfocus="this.style.borderColor='#3b82f6'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'; this.style.transform='translateY(-1px)';"
                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'; this.style.transform='translateY(0)';"
              />
              <div
                style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  bottom: 0;
                  padding-right: 12px;
                  display: flex;
                  align-items: center;
                "
              >
                <svg
                  style="width: 20px; height: 20px; color: #9ca3af"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
            </div>
            <p v-if="errors.username" style="color: #ef4444; font-size: 0.75rem; margin-top: 4px">
              {{ errors.username }}
            </p>
          </div>

          <!-- 密码输入 -->
          <div style="display: flex; flex-direction: column; gap: 8px">
            <label style="font-size: 0.875rem; font-weight: 500; color: #374151; display: block"
              >密码</label
            >
            <div style="position: relative">
              <input
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                required
                style="
                  width: 100%;
                  padding: 12px 16px;
                  background: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 12px;
                  outline: none;
                  transition: all 0.2s;
                  font-size: 1rem;
                  padding-right: 48px;
                "
                placeholder="请输入密码"
                onfocus="this.style.borderColor='#3b82f6'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'; this.style.transform='translateY(-1px)';"
                onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'; this.style.transform='translateY(0)';"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  bottom: 0;
                  padding-right: 12px;
                  display: flex;
                  align-items: center;
                  background: none;
                  border: none;
                  cursor: pointer;
                "
              >
                <svg
                  v-if="showPassword"
                  style="width: 20px; height: 20px; color: #9ca3af; transition: color 0.2s"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  onmouseover="this.style.color='#6b7280'"
                  onmouseout="this.style.color='#9ca3af'"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                <svg
                  v-else
                  style="width: 20px; height: 20px; color: #9ca3af; transition: color 0.2s"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  onmouseover="this.style.color='#6b7280'"
                  onmouseout="this.style.color='#9ca3af'"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                  />
                </svg>
              </button>
            </div>
            <p v-if="errors.password" style="color: #ef4444; font-size: 0.75rem; margin-top: 4px">
              {{ errors.password }}
            </p>
          </div>

          <!-- 记住我和忘记密码 -->
          <div style="display: flex; align-items: center; justify-content: space-between">
            <label style="display: flex; align-items: center">
              <input
                v-model="form.rememberMe"
                type="checkbox"
                style="
                  width: 16px;
                  height: 16px;
                  color: #3b82f6;
                  background: #f3f4f6;
                  border: 1px solid #d1d5db;
                  border-radius: 4px;
                  margin-right: 8px;
                "
              />
              <span style="font-size: 0.875rem; color: #4b5563">记住我</span>
            </label>
            <a
              href="#"
              style="font-size: 0.875rem; color: #3b82f6; font-weight: 500; text-decoration: none"
              onmouseover="this.style.color='#1d4ed8'"
              onmouseout="this.style.color='#3b82f6'"
            >
              忘记密码？
            </a>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="!isFormValid || loading"
            style="
              width: 100%;
              background: linear-gradient(to right, #3b82f6, #9333ea);
              color: white;
              padding: 12px 16px;
              border-radius: 12px;
              font-weight: 500;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
              transition: all 0.2s;
              border: none;
              cursor: pointer;
            "
            onmouseover="if(!this.disabled) { this.style.boxShadow='0 20px 25px -5px rgba(0, 0, 0, 0.1)'; this.style.transform='scale(1.02)'; }"
            onmouseout="if(!this.disabled) { this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'; this.style.transform='scale(1)'; }"
          >
            <div v-if="loading" style="display: flex; align-items: center; justify-content: center">
              <svg
                style="
                  animation: spin 1s linear infinite;
                  margin-left: -4px;
                  margin-right: 12px;
                  width: 20px;
                  height: 20px;
                  color: white;
                "
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              登录中...
            </div>
            <span v-else>登录</span>
          </button>
        </form>
      </div>

      <!-- 底部信息 -->
      <div style="padding: 0 32px 32px 32px; text-align: center">
        <div style="font-size: 0.75rem; color: #9ca3af; line-height: 1.5">
          <p>默认账户: admin / admin123</p>
          <p>&copy; 2024 图纸管理系统. 保留所有权利.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

const router = useRouter();
const authStore = useAuthStore();

// 表单数据
const form = ref({
  username: "",
  password: "",
  rememberMe: false,
});

// 错误信息
const errors = ref({
  username: "",
  password: "",
});

// 加载状态
const loading = ref(false);

// 密码显示状态
const showPassword = ref(false);

// 表单验证
const isFormValid = computed(() => {
  return form.value.username.trim() !== "" && form.value.password.trim() !== "";
});

// 处理登录
const handleLogin = async () => {
  // 清除之前的错误
  errors.value = { username: "", password: "" };

  if (!isFormValid.value) {
    return;
  }

  loading.value = true;

  try {
    await authStore.login({
      username: form.value.username,
      password: form.value.password,
    });

    // 登录成功，跳转到首页
    router.push("/");
  } catch (error: any) {
    console.error("登录失败:", error);

    // 处理错误信息
    if (error.response?.status === 401) {
      errors.value.password = "用户名或密码错误";
    } else {
      errors.value.password = "登录失败，请稍后重试";
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 动画效果 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 输入框聚焦效果 */
input:focus {
  transform: translateY(-1px);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 玻璃态效果 */
.backdrop-blur-xl {
  backdrop-filter: blur(20px);
}

/* 渐变文字效果 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}
</style>
