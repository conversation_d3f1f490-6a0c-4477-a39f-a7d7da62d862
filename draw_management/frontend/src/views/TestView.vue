<template>
  <div style="padding: 20px;">
    <h1>测试页面</h1>
    
    <!-- 测试小图标 -->
    <div style="margin: 20px 0;">
      <h2>小图标测试</h2>
      <svg style="width: 16px; height: 16px; color: blue;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      <span>16px图标</span>
    </div>
    
    <!-- 测试中等图标 -->
    <div style="margin: 20px 0;">
      <h2>中等图标测试</h2>
      <svg style="width: 24px; height: 24px; color: green;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <span>24px图标</span>
    </div>
    
    <!-- 测试CSS类图标 -->
    <div style="margin: 20px 0;">
      <h2>CSS类图标测试</h2>
      <svg class="icon-sm" style="color: red;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      <span>icon-sm类</span>
    </div>
    
    <!-- 测试Tailwind类图标 -->
    <div style="margin: 20px 0;">
      <h2>Tailwind类图标测试</h2>
      <svg class="h-6 w-6" style="color: purple;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      <span>h-6 w-6类</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 简单的测试页面
</script>

<style scoped>
.icon-sm {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
}
</style>
