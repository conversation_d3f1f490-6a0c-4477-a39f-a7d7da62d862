<template>
  <div
    style="
      min-height: 100vh;
      background: linear-gradient(135deg, #f8fafc 0%, #dbeafe 50%, #e0e7ff 100%);
      position: relative;
      overflow: hidden;
    "
  >
    <!-- 背景装饰 -->
    <div
      style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        pointer-events: none;
      "
    >
      <div
        style="
          position: absolute;
          top: -100px;
          right: -100px;
          width: 200px;
          height: 200px;
          background: #60a5fa;
          border-radius: 50%;
          mix-blend-mode: multiply;
          filter: blur(40px);
          opacity: 0.1;
          animation: blob 7s infinite;
        "
      ></div>
      <div
        style="
          position: absolute;
          bottom: -100px;
          left: -100px;
          width: 200px;
          height: 200px;
          background: #a78bfa;
          border-radius: 50%;
          mix-blend-mode: multiply;
          filter: blur(40px);
          opacity: 0.1;
          animation: blob 7s infinite;
          animation-delay: 2s;
        "
      ></div>
    </div>

    <!-- 主要内容 -->
    <div style="position: relative; z-index: 10; padding: 24px; max-width: 1200px; margin: 0 auto">
      <!-- 顶部导航栏 -->
      <div
        style="
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 20px;
          padding: 20px 32px;
          margin-bottom: 32px;
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 16px;
        "
      >
        <div style="flex: 1; min-width: 0">
          <h1
            style="
              font-size: 1.875rem;
              font-weight: bold;
              background: linear-gradient(to right, #111827, #4b5563);
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
              margin-bottom: 8px;
            "
          >
            欢迎回来，{{ authStore.user?.full_name }}
          </h1>
          <p style="color: #6b7280; font-size: 0.875rem">
            今天是 {{ currentDate }}，您有 {{ stats.pendingTasks }} 个待处理任务
          </p>
        </div>
        <button
          @click="showCreateModal = true"
          style="
            background: linear-gradient(to right, #3b82f6, #9333ea);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 500;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
          "
          onmouseover="this.style.boxShadow='0 20px 25px -5px rgba(0, 0, 0, 0.1)'; this.style.transform='scale(1.02)';"
          onmouseout="this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'; this.style.transform='scale(1)';"
        >
          <svg
            style="width: 16px; height: 16px"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          新建图纸
        </button>
      </div>

      <!-- 统计卡片 -->
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 24px;
          margin-bottom: 32px;
        "
      >
        <div
          v-for="stat in statsCards"
          :key="stat.name"
          style="
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
          "
          onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 20px 40px -10px rgba(0, 0, 0, 0.15)';"
          onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.1)';"
        >
          <div style="display: flex; align-items: center; gap: 16px">
            <div
              :style="`
                width: 56px;
                height: 56px;
                background: ${stat.iconBackground};
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
              `"
            >
              <svg
                style="width: 24px; height: 24px"
                :style="`color: ${stat.iconColor}`"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div style="flex: 1">
              <p style="font-size: 0.875rem; color: #6b7280; margin-bottom: 4px">{{ stat.name }}</p>
              <div style="display: flex; align-items: baseline; gap: 8px">
                <span style="font-size: 1.875rem; font-weight: bold; color: #111827">{{
                  stat.value
                }}</span>
                <div
                  :style="`
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    color: ${stat.changeType === 'increase' ? '#059669' : '#dc2626'};
                  `"
                >
                  <svg
                    v-if="stat.changeType === 'increase'"
                    style="width: 12px; height: 12px"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <svg
                    v-else
                    style="width: 12px; height: 12px"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动和快速操作 -->
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 32px;
          margin-bottom: 32px;
        "
      >
        <!-- 最近图纸 -->
        <div
          style="
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
          "
        >
          <h3
            style="
              font-size: 1.125rem;
              font-weight: 600;
              color: #111827;
              margin-bottom: 20px;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <svg
              style="width: 20px; height: 20px; color: #3b82f6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            最近图纸
          </h3>
          <div style="display: flex; flex-direction: column; gap: 12px">
            <div
              v-for="drawing in recentDrawings"
              :key="drawing.id"
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px;
                background: #f8fafc;
                border-radius: 12px;
                transition: all 0.2s;
                cursor: pointer;
              "
              onmouseover="this.style.background='#f1f5f9'; this.style.transform='translateX(4px)';"
              onmouseout="this.style.background='#f8fafc'; this.style.transform='translateX(0)';"
            >
              <div style="display: flex; align-items: center; gap: 12px">
                <div
                  style="
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <svg
                    style="width: 20px; height: 20px; color: #3b82f6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div>
                  <p
                    style="
                      font-size: 0.875rem;
                      font-weight: 500;
                      color: #111827;
                      margin-bottom: 2px;
                    "
                  >
                    {{ drawing.title }}
                  </p>
                  <p style="font-size: 0.75rem; color: #6b7280">{{ drawing.drawing_number }}</p>
                </div>
              </div>
              <div style="text-align: right">
                <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 4px">
                  {{ formatDate(drawing.created_at) }}
                </p>
                <span
                  :style="`
                    display: inline-flex;
                    align-items: center;
                    padding: 2px 8px;
                    border-radius: 6px;
                    font-size: 0.75rem;
                    font-weight: 500;
                    ${getStatusStyle(drawing.status)}
                  `"
                >
                  {{ getStatusText(drawing.status) }}
                </span>
              </div>
            </div>
          </div>
          <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb">
            <router-link
              to="/drawings"
              style="
                font-size: 0.875rem;
                font-weight: 500;
                color: #3b82f6;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 4px;
                transition: color 0.2s;
              "
              onmouseover="this.style.color='#1d4ed8'"
              onmouseout="this.style.color='#3b82f6'"
            >
              查看所有图纸
              <svg
                style="width: 16px; height: 16px"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </router-link>
          </div>
        </div>

        <!-- 快速操作 -->
        <div
          style="
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
          "
        >
          <h3
            style="
              font-size: 1.125rem;
              font-weight: 600;
              color: #111827;
              margin-bottom: 20px;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <svg
              style="width: 20px; height: 20px; color: #9333ea"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            快速操作
          </h3>
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px">
            <button
              v-for="action in quickActions"
              :key="action.name"
              @click="action.action"
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
                background: #f8fafc;
                border-radius: 16px;
                transition: all 0.3s ease;
                border: none;
                cursor: pointer;
                text-align: center;
              "
              onmouseover="this.style.background='#f1f5f9'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px -4px rgba(0, 0, 0, 0.1)';"
              onmouseout="this.style.background='#f8fafc'; this.style.transform='translateY(0)'; this.style.boxShadow='none';"
            >
              <div
                :style="`
                  width: 48px;
                  height: 48px;
                  background: ${action.iconBackground};
                  border-radius: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 12px;
                  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
                `"
              >
                <svg
                  style="width: 24px; height: 24px"
                  :style="`color: ${action.iconColor}`"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
              <span style="font-size: 0.875rem; font-weight: 500; color: #111827">{{
                action.name
              }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建图纸模态框 -->
    <div
      v-if="showCreateModal"
      style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(8px);
        z-index: 50;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
      "
      @click.self="showCreateModal = false"
    >
      <div
        style="
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 24px;
          padding: 32px;
          max-width: 500px;
          width: 100%;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transform: scale(0.95);
          animation: modalShow 0.2s ease-out forwards;
        "
      >
        <div style="text-align: center; margin-bottom: 24px">
          <div
            style="
              width: 64px;
              height: 64px;
              background: linear-gradient(135deg, #3b82f6, #9333ea);
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 16px auto;
            "
          >
            <svg
              style="width: 28px; height: 28px; color: white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
          </div>
          <h3 style="font-size: 1.25rem; font-weight: 600; color: #111827; margin-bottom: 8px">
            创建新图纸
          </h3>
          <p style="color: #6b7280; font-size: 0.875rem">创建图纸表单将在这里显示</p>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center">
          <button
            @click="showCreateModal = false"
            style="
              padding: 12px 24px;
              background: #f3f4f6;
              color: #374151;
              border-radius: 12px;
              font-weight: 500;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#e5e7eb'"
            onmouseout="this.style.background='#f3f4f6'"
          >
            取消
          </button>
          <button
            @click="showCreateModal = false"
            style="
              padding: 12px 24px;
              background: linear-gradient(to right, #3b82f6, #9333ea);
              color: white;
              border-radius: 12px;
              font-weight: 500;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.transform='scale(1.02)'"
            onmouseout="this.style.transform='scale(1)'"
          >
            创建
          </button>
        </div>

        <!-- 快速操作 -->
        <div
          style="
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
          "
        >
          <h3
            style="
              font-size: 1.125rem;
              font-weight: 600;
              color: #111827;
              margin-bottom: 20px;
              margin-top: 0;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <svg
              style="width: 20px; height: 20px; color: #9333ea"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            快速操作
          </h3>
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px">
            <button
              v-for="action in quickActions"
              :key="action.name"
              @click="action.action"
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
                background: #f8fafc;
                border-radius: 16px;
                transition: all 0.3s ease;
                border: none;
                cursor: pointer;
                text-align: center;
              "
              onmouseover="this.style.background='#f1f5f9'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px -4px rgba(0, 0, 0, 0.1)';"
              onmouseout="this.style.background='#f8fafc'; this.style.transform='translateY(0)'; this.style.boxShadow='none';"
            >
              <div
                :style="`
                  width: 48px;
                  height: 48px;
                  background: ${action.iconBackground};
                  border-radius: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 12px;
                  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
                `"
              >
                <svg
                  style="width: 24px; height: 24px"
                  :style="`color: ${action.iconColor}`"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
              <span style="font-size: 0.875rem; font-weight: 500; color: #111827">{{
                action.name
              }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建图纸模态框 -->
    <div
      v-if="showCreateModal"
      style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(8px);
        z-index: 50;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
      "
      @click.self="showCreateModal = false"
    >
      <div
        style="
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 24px;
          padding: 32px;
          max-width: 500px;
          width: 100%;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transform: scale(0.95);
          animation: modalShow 0.2s ease-out forwards;
        "
      >
        <div style="text-align: center; margin-bottom: 24px">
          <div
            style="
              width: 64px;
              height: 64px;
              background: linear-gradient(135deg, #3b82f6, #9333ea);
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 16px auto;
            "
          >
            <svg
              style="width: 28px; height: 28px; color: white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
          </div>
          <h3
            style="
              font-size: 1.25rem;
              font-weight: 600;
              color: #111827;
              margin-bottom: 8px;
              margin-top: 0;
            "
          >
            创建新图纸
          </h3>
          <p style="color: #6b7280; font-size: 0.875rem; margin: 0">创建图纸表单将在这里显示</p>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center">
          <button
            @click="showCreateModal = false"
            style="
              padding: 12px 24px;
              background: #f3f4f6;
              color: #374151;
              border-radius: 12px;
              font-weight: 500;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.background='#e5e7eb'"
            onmouseout="this.style.background='#f3f4f6'"
          >
            取消
          </button>
          <button
            @click="showCreateModal = false"
            style="
              padding: 12px 24px;
              background: linear-gradient(to right, #3b82f6, #9333ea);
              color: white;
              border-radius: 12px;
              font-weight: 500;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.transform='scale(1.02)'"
            onmouseout="this.style.transform='scale(1)'"
          >
            创建
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useDrawingsStore } from "@/stores/drawings";

const authStore = useAuthStore();
const drawingsStore = useDrawingsStore();

const showCreateModal = ref(false);

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
});

// 统计数据
const stats = ref({
  totalDrawings: 0,
  pendingReviews: 0,
  completedThisMonth: 0,
  pendingTasks: 0,
});

// 最近图纸
const recentDrawings = ref([]);

// 统计卡片数据
const statsCards = computed(() => [
  {
    name: "总图纸数",
    value: stats.value.totalDrawings,
    change: "+12%",
    changeType: "increase",
    icon: "DocumentIcon",
    iconColor: "#3b82f6",
    iconBackground: "linear-gradient(135deg, #dbeafe, #bfdbfe)",
  },
  {
    name: "待审核",
    value: stats.value.pendingReviews,
    change: "+2",
    changeType: "increase",
    icon: "ClockIcon",
    iconColor: "#f59e0b",
    iconBackground: "linear-gradient(135deg, #fef3c7, #fde68a)",
  },
  {
    name: "本月完成",
    value: stats.value.completedThisMonth,
    change: "+8%",
    changeType: "increase",
    icon: "CheckIcon",
    iconColor: "#10b981",
    iconBackground: "linear-gradient(135deg, #d1fae5, #a7f3d0)",
  },
  {
    name: "待处理任务",
    value: stats.value.pendingTasks,
    change: "-3",
    changeType: "decrease",
    icon: "ExclamationIcon",
    iconColor: "#ef4444",
    iconBackground: "linear-gradient(135deg, #fee2e2, #fecaca)",
  },
]);

// 快速操作
const quickActions = [
  {
    name: "上传图纸",
    icon: "UploadIcon",
    iconColor: "#3b82f6",
    iconBackground: "linear-gradient(135deg, #dbeafe, #bfdbfe)",
    action: () => (showCreateModal.value = true),
  },
  {
    name: "创建模板",
    icon: "TemplateIcon",
    iconColor: "#10b981",
    iconBackground: "linear-gradient(135deg, #d1fae5, #a7f3d0)",
    action: () => console.log("创建模板"),
  },
  {
    name: "导入数据",
    icon: "DatabaseIcon",
    iconColor: "#8b5cf6",
    iconBackground: "linear-gradient(135deg, #ede9fe, #ddd6fe)",
    action: () => console.log("导入数据"),
  },
  {
    name: "生成报告",
    icon: "ChartIcon",
    iconColor: "#f59e0b",
    iconBackground: "linear-gradient(135deg, #fef3c7, #fde68a)",
    action: () => console.log("生成报告"),
  },
];

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("zh-CN");
};

// 获取状态样式
const getStatusStyle = (status: string) => {
  const styles = {
    draft: "background: #f3f4f6; color: #374151;",
    review: "background: #fef3c7; color: #92400e;",
    approved: "background: #d1fae5; color: #065f46;",
    archived: "background: #dbeafe; color: #1e40af;",
    obsolete: "background: #fee2e2; color: #991b1b;",
  };
  return styles[status as keyof typeof styles] || "background: #f3f4f6; color: #374151;";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    draft: "草稿",
    review: "审核中",
    approved: "已批准",
    archived: "已归档",
    obsolete: "已废弃",
  };
  return texts[status as keyof typeof texts] || "未知";
};

// 加载数据
onMounted(async () => {
  try {
    // 加载最近图纸
    await drawingsStore.fetchDrawings({ size: 5 });
    recentDrawings.value = drawingsStore.drawings.slice(0, 5);

    // 更新统计数据
    stats.value.totalDrawings = drawingsStore.pagination.total;
    // 这里可以添加更多统计数据的计算
  } catch (error) {
    console.error("加载数据失败:", error);
  }
});
</script>

<style scoped>
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes modalShow {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-responsive {
    grid-template-columns: 1fr !important;
  }
}
</style>
