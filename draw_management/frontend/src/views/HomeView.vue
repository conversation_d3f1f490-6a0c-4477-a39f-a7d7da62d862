<template>
  <div>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl">
            欢迎回来，{{ authStore.user?.full_name }}
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            今天是 {{ currentDate }}，您有 {{ stats.pendingTasks }} 个待处理任务
          </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
          <Button @click="showCreateModal = true">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
            新建图纸
          </Button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <Card v-for="stat in statsCards" :key="stat.name" class="overflow-hidden">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div :class="[stat.iconBackground, 'rounded-md p-3']">
                  <svg
                    class="h-6 w-6"
                    :class="stat.iconColor"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">{{ stat.name }}</dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">{{ stat.value }}</div>
                    <div
                      :class="[
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
                        'ml-2 flex items-baseline text-sm font-semibold',
                      ]"
                    >
                      <svg
                        v-if="stat.changeType === 'increase'"
                        class="h-3 w-3 flex-shrink-0 self-center text-green-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <svg
                        v-else
                        class="h-3 w-3 flex-shrink-0 self-center text-red-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      {{ stat.change }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <!-- 最近活动和快速操作 -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- 最近图纸 -->
        <Card title="最近图纸">
          <div class="space-y-3">
            <div
              v-for="drawing in recentDrawings"
              :key="drawing.id"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div class="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg
                    class="h-5 w-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ drawing.title }}</p>
                  <p class="text-xs text-gray-500">{{ drawing.drawing_number }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-xs text-gray-500">{{ formatDate(drawing.created_at) }}</p>
                <span
                  :class="getStatusColor(drawing.status)"
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                >
                  {{ getStatusText(drawing.status) }}
                </span>
              </div>
            </div>
          </div>
          <template #footer>
            <router-link
              to="/drawings"
              class="text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              查看所有图纸 →
            </router-link>
          </template>
        </Card>

        <!-- 快速操作 -->
        <Card title="快速操作">
          <div class="grid grid-cols-2 gap-4">
            <button
              v-for="action in quickActions"
              :key="action.name"
              @click="action.action"
              class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div :class="[action.iconBackground, 'rounded-lg p-3 mb-3']">
                <svg
                  class="h-6 w-6"
                  :class="action.iconColor"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ action.name }}</span>
            </button>
          </div>
        </Card>
      </div>
    </div>

    <!-- 创建图纸模态框 -->
    <Modal :show="showCreateModal" @close="showCreateModal = false" title="创建新图纸" size="lg">
      <!-- 这里可以添加创建图纸的表单 -->
      <div class="text-center py-8">
        <p class="text-gray-500">创建图纸表单将在这里显示</p>
      </div>
      <template #footer>
        <Button variant="secondary" @click="showCreateModal = false">取消</Button>
        <Button @click="showCreateModal = false">创建</Button>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useDrawingsStore } from "@/stores/drawings";
import Card from "@/components/ui/Card.vue";
import Button from "@/components/ui/Button.vue";
import Modal from "@/components/ui/Modal.vue";

const authStore = useAuthStore();
const drawingsStore = useDrawingsStore();

const showCreateModal = ref(false);

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });
});

// 统计数据
const stats = ref({
  totalDrawings: 0,
  pendingReviews: 0,
  completedThisMonth: 0,
  pendingTasks: 0,
});

// 最近图纸
const recentDrawings = ref([]);

// 统计卡片数据
const statsCards = computed(() => [
  {
    name: "总图纸数",
    value: stats.value.totalDrawings,
    change: "+12%",
    changeType: "increase",
    icon: "DocumentIcon",
    iconColor: "text-blue-600",
    iconBackground: "bg-blue-100",
  },
  {
    name: "待审核",
    value: stats.value.pendingReviews,
    change: "+2",
    changeType: "increase",
    icon: "ClockIcon",
    iconColor: "text-yellow-600",
    iconBackground: "bg-yellow-100",
  },
  {
    name: "本月完成",
    value: stats.value.completedThisMonth,
    change: "+8%",
    changeType: "increase",
    icon: "CheckIcon",
    iconColor: "text-green-600",
    iconBackground: "bg-green-100",
  },
  {
    name: "待处理任务",
    value: stats.value.pendingTasks,
    change: "-3",
    changeType: "decrease",
    icon: "ExclamationIcon",
    iconColor: "text-red-600",
    iconBackground: "bg-red-100",
  },
]);

// 快速操作
const quickActions = [
  {
    name: "上传图纸",
    icon: "UploadIcon",
    iconColor: "text-blue-600",
    iconBackground: "bg-blue-100",
    action: () => (showCreateModal.value = true),
  },
  {
    name: "创建模板",
    icon: "TemplateIcon",
    iconColor: "text-green-600",
    iconBackground: "bg-green-100",
    action: () => console.log("创建模板"),
  },
  {
    name: "导入数据",
    icon: "DatabaseIcon",
    iconColor: "text-purple-600",
    iconBackground: "bg-purple-100",
    action: () => console.log("导入数据"),
  },
  {
    name: "生成报告",
    icon: "ChartIcon",
    iconColor: "text-orange-600",
    iconBackground: "bg-orange-100",
    action: () => console.log("生成报告"),
  },
];

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("zh-CN");
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    draft: "bg-gray-100 text-gray-800",
    review: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    archived: "bg-blue-100 text-blue-800",
    obsolete: "bg-red-100 text-red-800",
  };
  return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    draft: "草稿",
    review: "审核中",
    approved: "已批准",
    archived: "已归档",
    obsolete: "已废弃",
  };
  return texts[status as keyof typeof texts] || "未知";
};

// 加载数据
onMounted(async () => {
  try {
    // 加载最近图纸
    await drawingsStore.fetchDrawings({ size: 5 });
    recentDrawings.value = drawingsStore.drawings.slice(0, 5);

    // 更新统计数据
    stats.value.totalDrawings = drawingsStore.pagination.total;
    // 这里可以添加更多统计数据的计算
  } catch (error) {
    console.error("加载数据失败:", error);
  }
});
</script>
