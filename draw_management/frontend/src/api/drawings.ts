import api from './index'

export interface Drawing {
  id: number
  drawing_number: string
  title: string
  description?: string
  version: string
  drawing_type: 'product' | 'outsourced' | 'template' | 'training'
  category?: string
  subcategory?: string
  status: 'draft' | 'review' | 'approved' | 'archived' | 'obsolete'
  file_path?: string
  file_size?: number
  file_format?: string
  thumbnail_path?: string
  material?: string
  dimensions?: string
  weight?: number
  tolerance?: string
  keywords?: string
  tags?: string
  created_by: number
  creator_name?: string
  created_at: string
  updated_at?: string
}

export interface DrawingCreate {
  drawing_number: string
  title: string
  description?: string
  version?: string
  drawing_type: 'product' | 'outsourced' | 'template' | 'training'
  category?: string
  subcategory?: string
  material?: string
  dimensions?: string
  weight?: number
  tolerance?: string
  keywords?: string
  tags?: string
}

export interface DrawingUpdate {
  title?: string
  description?: string
  version?: string
  category?: string
  subcategory?: string
  status?: 'draft' | 'review' | 'approved' | 'archived' | 'obsolete'
  material?: string
  dimensions?: string
  weight?: number
  tolerance?: string
  keywords?: string
  tags?: string
}

export interface DrawingSearchParams {
  page?: number
  size?: number
  drawing_type?: string
  category?: string
  status?: string
  keyword?: string
}

export interface DrawingListResponse {
  items: Drawing[]
  total: number
  page: number
  size: number
  pages: number
}

// 获取图纸列表
export const getDrawings = async (params: DrawingSearchParams = {}): Promise<DrawingListResponse> => {
  const response = await api.get('/drawings/', { params })
  return response.data
}

// 获取图纸详情
export const getDrawing = async (id: number): Promise<Drawing> => {
  const response = await api.get(`/drawings/${id}`)
  return response.data
}

// 创建图纸
export const createDrawing = async (data: DrawingCreate): Promise<Drawing> => {
  const response = await api.post('/drawings/', data)
  return response.data
}

// 更新图纸
export const updateDrawing = async (id: number, data: DrawingUpdate): Promise<Drawing> => {
  const response = await api.put(`/drawings/${id}`, data)
  return response.data
}

// 删除图纸
export const deleteDrawing = async (id: number): Promise<void> => {
  await api.delete(`/drawings/${id}`)
}

// 上传图纸文件
export const uploadDrawingFile = async (id: number, file: File): Promise<any> => {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await api.post(`/drawings/${id}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return response.data
}
