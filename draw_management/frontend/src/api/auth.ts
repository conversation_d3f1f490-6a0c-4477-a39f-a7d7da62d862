import api from './index'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

export interface UserInfo {
  id: number
  username: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  is_active: boolean
}

// 用户登录
export const login = async (data: LoginRequest): Promise<LoginResponse> => {
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  
  const response = await api.post('/auth/login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return response.data
}

// 获取当前用户信息
export const getCurrentUser = async (): Promise<UserInfo> => {
  const response = await api.get('/auth/me')
  return response.data
}

// 用户登出
export const logout = () => {
  localStorage.removeItem('access_token')
  localStorage.removeItem('user_info')
}
