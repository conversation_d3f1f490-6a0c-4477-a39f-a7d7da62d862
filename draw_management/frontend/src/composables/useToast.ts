import { ref, createApp, type App } from 'vue'
import Toast from '@/components/ui/Toast.vue'

interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
}

interface ToastInstance {
  id: string
  app: App
  container: HTMLDivElement
}

const toasts = ref<ToastInstance[]>([])

let toastId = 0

const createToast = (options: ToastOptions) => {
  const id = `toast-${++toastId}`
  
  // 创建容器
  const container = document.createElement('div')
  container.id = id
  document.body.appendChild(container)
  
  // 创建Vue应用实例
  const app = createApp(Toast, {
    ...options,
    show: true,
    onClose: () => {
      removeToast(id)
    }
  })
  
  // 挂载应用
  app.mount(container)
  
  // 保存实例
  const instance: ToastInstance = {
    id,
    app,
    container
  }
  
  toasts.value.push(instance)
  
  return instance
}

const removeToast = (id: string) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    const toast = toasts.value[index]
    
    // 卸载应用
    toast.app.unmount()
    
    // 移除DOM元素
    if (toast.container.parentNode) {
      toast.container.parentNode.removeChild(toast.container)
    }
    
    // 从数组中移除
    toasts.value.splice(index, 1)
  }
}

export const useToast = () => {
  const success = (title: string, message?: string, duration = 5000) => {
    return createToast({
      type: 'success',
      title,
      message,
      duration
    })
  }
  
  const error = (title: string, message?: string, duration = 5000) => {
    return createToast({
      type: 'error',
      title,
      message,
      duration
    })
  }
  
  const warning = (title: string, message?: string, duration = 5000) => {
    return createToast({
      type: 'warning',
      title,
      message,
      duration
    })
  }
  
  const info = (title: string, message?: string, duration = 5000) => {
    return createToast({
      type: 'info',
      title,
      message,
      duration
    })
  }
  
  const clear = () => {
    toasts.value.forEach(toast => {
      removeToast(toast.id)
    })
  }
  
  return {
    success,
    error,
    warning,
    info,
    clear
  }
}
