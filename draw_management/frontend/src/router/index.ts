import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/login',
      name: 'login-page',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/home',
      component: () => import('../components/layout/AppLayout.vue'),
      children: [
        {
          path: '',
          name: 'home',
          component: () => import('../views/HomeView.vue')
        },
        {
          path: '/drawings',
          name: 'drawings',
          component: () => import('../views/DrawingsView.vue')
        }
      ]
    }
  ]
})

// 路由守卫 - 暂时禁用以便调试
// router.beforeEach(async (to, from, next) => {
//   next()
// })

export default router
