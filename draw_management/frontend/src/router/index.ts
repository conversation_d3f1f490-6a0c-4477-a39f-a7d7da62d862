import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      component: () => import('../components/layout/AppLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'home',
          component: () => import('../views/HomeView.vue')
        },
        {
          path: '/drawings',
          name: 'drawings',
          component: () => import('../views/DrawingsView.vue')
        },
        {
          path: '/drawings/product',
          name: 'product-drawings',
          component: () => import('../views/DrawingsView.vue'),
          props: { drawingType: 'product' }
        },
        {
          path: '/drawings/outsourced',
          name: 'outsourced-drawings',
          component: () => import('../views/DrawingsView.vue'),
          props: { drawingType: 'outsourced' }
        },
        {
          path: '/drawings/template',
          name: 'template-drawings',
          component: () => import('../views/DrawingsView.vue'),
          props: { drawingType: 'template' }
        },
        {
          path: '/drawings/training',
          name: 'training-drawings',
          component: () => import('../views/DrawingsView.vue'),
          props: { drawingType: 'training' }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  if (!authStore.user && localStorage.getItem('access_token')) {
    await authStore.initAuth()
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // 检查是否需要游客状态（如登录页）
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router
