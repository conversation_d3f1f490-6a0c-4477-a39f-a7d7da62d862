import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      component: () => import('../components/layout/AppLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'home',
          component: () => import('../views/HomeView.vue')
        },
        {
          path: '/drawings',
          name: 'drawings',
          component: () => import('../views/DrawingsView.vue')
        },
        {
          path: '/test',
          name: 'test',
          component: () => import('../views/TestView.vue')
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    const authStore = useAuthStore()

    // 初始化认证状态
    if (!authStore.user && localStorage.getItem('access_token')) {
      await authStore.initAuth()
    }

    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 检查是否需要游客状态（如登录页）
    if (to.meta.requiresGuest && authStore.isAuthenticated) {
      next('/')
      return
    }

    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    // 如果出错，直接跳转到登录页
    if (to.path !== '/login') {
      next('/login')
    } else {
      next()
    }
  }
})

export default router
