<script setup lang="ts">
import { RouterView } from "vue-router";
import { onMounted } from "vue";

// 应用初始化
onMounted(async () => {
  // 初始化认证状态
  console.log("应用已初始化");
});
</script>

<template>
  <RouterView />
</template>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial,
    sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 强制重置所有SVG图标 - 最高优先级 */
svg {
  max-width: 100% !important;
  max-height: 100% !important;
  flex-shrink: 0 !important;
  display: inline-block !important;
}

/* Tailwind CSS类的SVG尺寸控制 */
.h-4 {
  height: 1rem !important;
  width: 1rem !important;
}
.h-5 {
  height: 1.25rem !important;
  width: 1.25rem !important;
}
.h-6 {
  height: 1.5rem !important;
  width: 1.5rem !important;
}
.w-4 {
  width: 1rem !important;
  height: 1rem !important;
}
.w-5 {
  width: 1.25rem !important;
  height: 1.25rem !important;
}
.w-6 {
  width: 1.5rem !important;
  height: 1.5rem !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
