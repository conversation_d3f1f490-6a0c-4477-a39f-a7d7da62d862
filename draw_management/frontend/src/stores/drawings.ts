import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getDrawings,
  getDrawing,
  createDrawing,
  updateDrawing,
  deleteDrawing,
  uploadDrawingFile,
  type Drawing,
  type DrawingCreate,
  type DrawingUpdate,
  type DrawingSearchParams,
  type DrawingListResponse
} from '@/api/drawings'

export const useDrawingsStore = defineStore('drawings', () => {
  // 状态
  const drawings = ref<Drawing[]>([])
  const currentDrawing = ref<Drawing | null>(null)
  const loading = ref(false)
  const searchParams = ref<DrawingSearchParams>({
    page: 1,
    size: 20
  })
  const pagination = ref({
    total: 0,
    page: 1,
    size: 20,
    pages: 0
  })

  // 获取图纸列表
  const fetchDrawings = async (params?: DrawingSearchParams) => {
    loading.value = true
    try {
      const searchQuery = { ...searchParams.value, ...params }
      const response: DrawingListResponse = await getDrawings(searchQuery)
      
      drawings.value = response.items
      pagination.value = {
        total: response.total,
        page: response.page,
        size: response.size,
        pages: response.pages
      }
      
      // 更新搜索参数
      searchParams.value = searchQuery
    } catch (error) {
      console.error('获取图纸列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取图纸详情
  const fetchDrawing = async (id: number) => {
    loading.value = true
    try {
      const drawing = await getDrawing(id)
      currentDrawing.value = drawing
      return drawing
    } catch (error) {
      console.error('获取图纸详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建图纸
  const addDrawing = async (data: DrawingCreate) => {
    loading.value = true
    try {
      const newDrawing = await createDrawing(data)
      drawings.value.unshift(newDrawing)
      return newDrawing
    } catch (error) {
      console.error('创建图纸失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新图纸
  const editDrawing = async (id: number, data: DrawingUpdate) => {
    loading.value = true
    try {
      const updatedDrawing = await updateDrawing(id, data)
      
      // 更新列表中的图纸
      const index = drawings.value.findIndex(d => d.id === id)
      if (index !== -1) {
        drawings.value[index] = updatedDrawing
      }
      
      // 更新当前图纸
      if (currentDrawing.value?.id === id) {
        currentDrawing.value = updatedDrawing
      }
      
      return updatedDrawing
    } catch (error) {
      console.error('更新图纸失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除图纸
  const removeDrawing = async (id: number) => {
    loading.value = true
    try {
      await deleteDrawing(id)
      
      // 从列表中移除
      drawings.value = drawings.value.filter(d => d.id !== id)
      
      // 清除当前图纸
      if (currentDrawing.value?.id === id) {
        currentDrawing.value = null
      }
    } catch (error) {
      console.error('删除图纸失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 上传图纸文件
  const uploadFile = async (id: number, file: File) => {
    loading.value = true
    try {
      const result = await uploadDrawingFile(id, file)
      
      // 重新获取图纸信息以更新文件信息
      await fetchDrawing(id)
      
      return result
    } catch (error) {
      console.error('上传文件失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索图纸
  const searchDrawings = async (keyword: string) => {
    await fetchDrawings({ ...searchParams.value, keyword, page: 1 })
  }

  // 筛选图纸
  const filterDrawings = async (filters: Partial<DrawingSearchParams>) => {
    await fetchDrawings({ ...searchParams.value, ...filters, page: 1 })
  }

  // 分页
  const changePage = async (page: number) => {
    await fetchDrawings({ ...searchParams.value, page })
  }

  // 重置搜索
  const resetSearch = async () => {
    searchParams.value = { page: 1, size: 20 }
    await fetchDrawings()
  }

  return {
    drawings,
    currentDrawing,
    loading,
    searchParams,
    pagination,
    fetchDrawings,
    fetchDrawing,
    addDrawing,
    editDrawing,
    removeDrawing,
    uploadFile,
    searchDrawings,
    filterDrawings,
    changePage,
    resetSearch
  }
})
