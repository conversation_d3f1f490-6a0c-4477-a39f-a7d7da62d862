import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, getCurrentUser, logout as apiLogout, type LoginRequest, type UserInfo } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const token = ref<string | null>(localStorage.getItem('access_token'))
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const userRole = computed(() => user.value?.role || 'guest')

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await apiLogin(credentials)
      token.value = response.access_token
      localStorage.setItem('access_token', response.access_token)
      
      // 获取用户信息
      await fetchUserInfo()
      
      return true
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return
    
    try {
      const userInfo = await getCurrentUser()
      user.value = userInfo
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    apiLogout()
  }

  // 初始化认证状态
  const initAuth = async () => {
    const savedToken = localStorage.getItem('access_token')
    const savedUserInfo = localStorage.getItem('user_info')
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      try {
        user.value = JSON.parse(savedUserInfo)
        // 验证token是否仍然有效
        await fetchUserInfo()
      } catch (error) {
        logout()
      }
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    userRole,
    login,
    logout,
    fetchUserInfo,
    initAuth
  }
})
