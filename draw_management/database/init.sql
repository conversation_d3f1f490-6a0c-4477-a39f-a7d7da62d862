-- 图纸管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS draw_management;
USE draw_management;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    department VARCHAR(100),
    position VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'designer', 'engineer', 'viewer', 'guest') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    notes TEXT
);

-- 创建图纸表
CREATE TABLE IF NOT EXISTS drawings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    drawing_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    drawing_type ENUM('product', 'outsourced', 'template', 'training') NOT NULL,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    status ENUM('draft', 'review', 'approved', 'archived', 'obsolete') DEFAULT 'draft',
    file_path VARCHAR(500),
    file_size INT,
    file_format VARCHAR(20),
    thumbnail_path VARCHAR(500),
    material VARCHAR(100),
    dimensions VARCHAR(200),
    weight FLOAT,
    tolerance VARCHAR(100),
    keywords TEXT,
    tags TEXT,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_drawing_number (drawing_number),
    INDEX idx_drawing_type (drawing_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
);

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    document_type ENUM('manual', 'specification', 'report', 'presentation', 'spreadsheet', 'other') NOT NULL,
    category VARCHAR(100),
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(200) NOT NULL,
    file_size INT,
    file_format VARCHAR(20),
    uploaded_by INT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    access_level VARCHAR(50) DEFAULT 'internal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    keywords TEXT,
    tags TEXT,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_document_type (document_type),
    INDEX idx_uploaded_by (uploaded_by)
);

-- 创建外部链接表
CREATE TABLE IF NOT EXISTS external_links (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    url VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_checked TIMESTAMP NULL,
    keywords TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_category (category),
    INDEX idx_created_by (created_by)
);

-- 创建工作流表
CREATE TABLE IF NOT EXISTS workflows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    workflow_type VARCHAR(100),
    status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    created_by INT NOT NULL,
    assigned_to INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    due_date TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    progress_percentage INT DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_due_date (due_date)
);

-- 创建工作流任务表
CREATE TABLE IF NOT EXISTS workflow_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    workflow_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    task_order INT DEFAULT 0,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
    assigned_to INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    due_date TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    output_files TEXT,
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to)
);

-- 插入默认管理员用户
INSERT INTO users (username, email, hashed_password, full_name, role, is_active, is_verified) 
VALUES (
    'admin', 
    '<EMAIL>', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- 密码: admin123
    '系统管理员', 
    'admin', 
    TRUE, 
    TRUE
);

-- 插入示例数据
INSERT INTO users (username, email, hashed_password, full_name, department, position, role) VALUES
('designer1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '张设计师', '设计部', '高级设计师', 'designer'),
('engineer1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '李工程师', '工程部', '项目工程师', 'engineer'),
('viewer1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', '王查看员', '质量部', '质量检查员', 'viewer');

-- 插入示例图纸数据
INSERT INTO drawings (drawing_number, title, description, drawing_type, category, status, created_by) VALUES
('DWG-001', '主轴设计图', '机械主轴的详细设计图纸', 'product', '机械', 'approved', 2),
('DWG-002', '电路板布局图', 'PCB电路板的布局设计', 'product', '电气', 'review', 2),
('DWG-003', '外壳模具图', '产品外壳的注塑模具图', 'outsourced', '模具', 'draft', 3),
('TPL-001', '标准图框模板', '公司标准图纸框架模板', 'template', '标准', 'approved', 1),
('TRN-001', 'CAD操作手册', 'CAD软件使用培训手册', 'training', '培训', 'approved', 1);

-- 插入示例外部链接
INSERT INTO external_links (title, url, description, category, created_by) VALUES
('AutoCAD官方文档', 'https://help.autodesk.com/view/ACD/2024/CHS/', 'AutoCAD软件官方帮助文档', '软件工具', 1),
('机械设计手册', 'https://www.example.com/mechanical-handbook', '机械设计相关的技术手册', '技术资料', 1),
('ISO标准查询', 'https://www.iso.org/standards.html', '国际标准化组织标准查询', '标准规范', 1);

-- 插入示例工作流
INSERT INTO workflows (name, description, workflow_type, created_by, assigned_to) VALUES
('新产品图纸审核流程', '新产品设计图纸的标准审核流程', '图纸审核', 1, 2),
('模具设计验收流程', '外包模具设计的验收流程', '外包验收', 1, 3);

-- 插入示例工作流任务
INSERT INTO workflow_tasks (workflow_id, title, description, task_order, assigned_to) VALUES
(1, '初步设计审核', '对图纸的初步设计进行技术审核', 1, 2),
(1, '详细设计确认', '确认详细设计方案的可行性', 2, 3),
(1, '最终审批', '管理层对设计方案的最终审批', 3, 1),
(2, '模具设计检查', '检查外包模具设计的质量', 1, 3),
(2, '试模验收', '进行试模并验收模具质量', 2, 2);
